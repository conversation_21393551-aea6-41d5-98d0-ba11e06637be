/*                                     
							        _.-- ,.--.
							      .'   .'     /
							       @       |'..--------._
							     /      \._/              '.
							    /  .-.-                     \
							   (  /    \                     \
							   \\      '.                  | #
							    \\       \   -.           /
							     :\       |    )._____.'   \
							      "       |   /  \  |  \    )
							              |   |./'  :__ \.-'
							              '--'         
                                       ...-###########+.. ..                                        
                                      .=##############%###..                                        
                                    .=#############%########..                                      
                                 ...#########################:..                                    
                                  .*##-.....#########-....:###.                                     
                                 .-##........#######........=#*.                                    
                                 .*#...%%--:.:#####+..%%:+:..*#.                                    
                                 .##...+%%@...=----:..+%%%...=#.                                    
                                 .*%:......-====------.......*#..                                   
                                  :##:.....=+===----==-.....=#*.                                    
                                 ..*##::.....==++===:.....:*##:.                                    
                                 ..%%#%*:::.....=:.....::=#####. .                                  
                                 .#%%#%%%%*:::::::::::=########*...                                 
                              ...##%#%%#%+.............:########+.                                  
                              ..#%%%#%#*.................-#######:.                                 
                           ....##%%%%#=....................%#####%=..                               
                          ..:%%%%%%%%-......................######%%+. .                            
                         ..##%%%%%%%+........................%####%%%#-.                            
                        .+###%%%%%%#.........................:%%%#%%%###....                        
                      ..%##%%%%%%%%:..........................*%%###%%#%#=.  .                      
                     .-##%%%%%%%%%*............................%%%%%%%%%###...                      
                   ..=#%%%%%%%%%%%:............................#%%%%%%%%%###...                     
                 ...+###%%%%%%%%%%.............................=%%%%%%%%%%%%#.                      
                 ..=##%%%%%%%%%%%#..............................%%%%%%%%%%%%%#....                  
                 .:%##%%%%%-#%%%%*..............................%%%%%.*%%%%%#%*...                  
                 .##%%%%%-..*%%%%*..............................%%%%%...%%%%%%#:.                   
               ..+#%%%%*... +%%%%#.............................:%%%%%.. .:%%%%##.                   
               ..#%%%%....  :%%%%#.............................-%%%%*.    .#%%%%:                   
               ..%%%-..... ..%%%%%:............................*%%%%:   .....%%#+.                  
               ..-...   .. ..+%%%%=:..........................:%%%%#.   .......-.                   
                            ..%%%%%::........................:=%%%%-.                               
                             .:%%%%=:::....................:::%%%%+...                              
                              .:%%%%:::::...............:::::#%%%*..                                
                              ...%%%%-::::::::::::::::::::::#%%%=..                                 
                              ....+%%%%=::::::::::::::::::*%%%%......                               
                         ...........+%%%%%%#=:::::::-+#%%%%%#:............                          
                        .......:-======+#%%%%%%%%%%%%%%%%*=-=====:...........                       
                      .........:-============+-:::-+============-:...........                       
                      ...........:-==-====+=:.......:======--==-.............                       
                           ..............................................                           
                                 ................................          
                        	AUTHOR: KDUCKP -- EMAIL: <EMAIL>  
                        	VERSION: 1.0 - NOT UPDATED ANYTHING
*/

#include <bits/stdc++.h>

using namespace std;
using ll = long long;
using ull = unsigned long long;
using ld = long double;
using pii = pair<int, int>;
using pll = pair<ll, ll>;
using vi = vector<int>;
using vll = vector<ll>;
using vii = vector<pii>;
using vvi = vector<vi>;

#define endl "\n"
#define fi first
#define se second
#define pb push_back
#define eb emplace_back
#define all(x) (x).begin(), (x).end()
#define rall(x) (x).rbegin(), (x).rend()
#define sz(x) ((int)(x).size())
#define FOR(i, n) for (int i = 0; i < (n); ++i)
#define REP(i, a, b) for (int i = (a); i <= (b); ++i)
#define PER(i, a, b) for (int i = (a); i >= (b); --i)
#define EACH(x, a) for (auto& x : a)
#define fast ios::sync_with_stdio(false); cin.tie(nullptr);
#define YESNO(x) cout << ((x) ? "YES\n" : "NO\n")
#define bitcount(x) __builtin_popcountll(x)
#define clz(x) __builtin_clzll(x)
#define ctz(x) __builtin_ctzll(x)
#define lb lower_bound
#define ub upper_bound
#define __TOISETHIVOI__ int32_t main()
#define vec2d(type, name, r, c, val) vector<vector<type>> name((r), vector<type>((c), (val)))
#define vec3d(type, name, x, y, z, val) vector<vector<vector<type>>> name((x), vector<vector<type>>((y), vector<type>((z), (val))))


const int MOD = 1e9 + 7;
const int MOD2 = 998244353;
const ll INF = 2e18;
const int N = 2e5 + 5;
const int LOG = 20;
const double PI = acos(-1);
const double EPS = 1e-9;

vi a;
__TOISETHIVOI__{
	fast;



	return 0;
}
