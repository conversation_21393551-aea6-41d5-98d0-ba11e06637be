/*
THEM LAZY VO LA FULL
THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL


THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL

THEM LAZY VO LA FULL
THEM LAZY VO LA FULL


 */
#include <bits/stdc++.h>

using namespace std;

#define ll long long
#define int long long
#define vi vector<int>
#define pii pair<int, int>
#define vvi vector<vi>
#define vii vector<pii>
#define ull unsigned long long
#define ld long double
#define setp(x) fixed << setprecision(x)
#define setp2(x, y) fixed << setprecision(x) << y
#define endl "\n"
#define __TOISETHIVOI__ signed main()
#define all(x) x.begin(), x.end()
#define pb push_back
#define mp make_pair
#define fast                      \
    ios_base::sync_with_stdio(0); \
    cin.tie(0);                   \
    cout.tie(0);
#define fi first
#define se second
#define rall(x) (x).rbegin(), (x).rend()
#define sz(x) (int)(x).size()
#define yes cout << "YES\n"
#define no cout << "NO\n"
#define YESNO(x) cout << ((x) ? "YES\n" : "NO\n")
#define debug(x) cerr << #x << " = " << x << endl
#define debug2(x, y) cerr << #x << " = " << x << ", " << #y << " = " << y << endl
#define debug3(x, y, z) cerr << #x << " = " << x << ", " << #y << " = " << y << ", " << #z << " = " << z << endl
#define kill cout << "end" << endl;
#define traillingzero(x) __builtin_ctzll(x)
#define count_bit_1(x) __builtin_popcountll(x)
#define leadingzero(x) __builtin_clzll(x)

const int MOD = 1e9 + 7;
const int MOD2 = 998244353;
const int INF = INT_MAX;
const int N = 3e5 + 5;
const int MAXVAL = 1e6 + 5;
const int LOG = 20;
const double PI = acos(-1);
const double EPS = 1e-9;

int divisor_count[MAXVAL];

void OJ()
{
    fast;
}
void THEMIS()
{
    freopen("text.inp", "r", stdin);
    freopen("text.out", "w", stdout);
    fast;
}

void precompute_divisors()
{
    for (int i = 1; i < MAXVAL; i++)
    {
        for (int j = i; j < MAXVAL; j += i)
        {
            divisor_count[j]++;
        }
    }
}
int dem_uoc(int n)
{
    if (n < MAXVAL) return divisor_count[n];

    int dem = 0;
    for (int i = 1; i * i <= n; i++)
    {
        if (n % i == 0)
        {
            dem++;
            if (i * i != n)
                dem++;
        }
    }
    return dem;
}
int n;
vi a;
int st[4 * N];
void build(int id, int l, int r)
{
    if (l == r)
    {
        // leaf node
        st[id] = a[l];
        return;
    }
    int mid = (l + r) / 2;
    build(2 * id, l, mid);
    build(2 * id + 1, mid + 1, r);
    st[id] = st[2 * id] + st[2 * id + 1];
}
void update(int id, int l, int r, int pos, int val)
{
    if (l == r)
    {
        // leaf node
        a[pos] = val;
        st[id] = val;
        return;
    }
    int mid = (l + r) / 2;
    if (pos <= mid)
        update(2 * id, l, mid, pos, val);
    else
        update(2 * id + 1, mid + 1, r, pos, val);
    st[id] = st[2 * id] + st[2 * id + 1];
}
int get(int id, int l, int r, int u, int v)
{
    if (l > v || r < u)
        return 0;
    if (l >= u && r <= v)
        return st[id];
    int mid = (l + r) / 2;
    int left = get(2 * id, l, mid, u, v);
    int right = get(2 * id + 1, mid + 1, r, u, v);
    return left + right;
}
void sol()
{
    int n, m;
    cin >> n >> m;
    a.resize(n + 1);
    for (int i = 1; i <= n; i++)
        cin >> a[i];
    build(1, 1, n);
    while (m--)
    {
        int type;
        cin >> type;
        if (type == 1)
        {
            int l, r;
            cin >> l >> r;
            for (int i = l; i <= r; i++)
            {
                int new_val = dem_uoc(a[i]);
                update(1, 1, n, i, new_val);
            }
        }
        else
        {
            int l, r;
            cin >> l >> r;
            if (l > r)
                swap(l, r);
            cout << get(1, 1, n, l, r) << endl;
        }
    }
}
__TOISETHIVOI__
{
    precompute_divisors();
    int t = 1;
    // cin >> t;
    for (int i = 0; i < t; i++)
    {
        sol();
    }
}